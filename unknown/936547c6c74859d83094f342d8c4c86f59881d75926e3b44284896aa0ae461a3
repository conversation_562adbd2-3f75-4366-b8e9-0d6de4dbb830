<html>

<head>
    <title>文档</title>
    <style>
        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        td,
        th {
            padding: 0;
        }

        .pure-table {
            border-collapse: collapse;
            border-spacing: 0;
            empty-cells: show;
            border: 1px solid #DCDFE6;
        }

        .pure-table caption {
            color: #000;
            font: italic 85%/1 arial, sans-serif;
            padding: 1em 0;
            text-align: center;
        }

        .pure-table td,
        .pure-table th {
            border-left: 1px solid #DCDFE6;
            border-width: 0 0 0 1px;
            font-size: inherit;
            margin: 0;
            overflow: visible;
            padding: .5em 1em;
        }

        .pure-table th {
            border-bottom: 1px solid #DCDFE6;
        }

        .pure-table thead {
            background-color: #f3f3f3;
            color: #000;
            text-align: left;
            vertical-align: bottom;
        }

        .pure-table td {
            background-color: transparent;
        }

        .pure-table-bordered td {
            border-bottom: 1px solid #DCDFE6;
        }

        .pure-table-bordered tbody>tr:last-child>td {
            border-bottom-width: 0;
        }

        .code-block {
            font-size: 14px;
            color: #606266;
            font-weight: normal;
            background-color: #fafafa;
            padding: 5px;
        }
    </style>
</head>

<body>
    <h1>文档</h1>
    <h2>发票设置表 前端控制器</h2>
    <div class="doc-item">
        <h3 id="Gez7092a"><a class="link" href="#Gez7092a">&nbsp;编辑发票设置</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST http://*************:9999/addon-invoice/invoice/invoiceSettings</li>
        </ul>
        <p><strong>描述：</strong>编辑发票设置</p>
        <p><strong>ContentType：</strong>application/json</p>
        <h4>请求参数</h4>
        <h5>Body Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>id</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>invoiceSetupValue</td>
                    <td>array</td>
                    <td>是</td>
                    <td></td>
                    <td>发票设置json<br>Validate[max: 1; ]</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ option</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>是否选中</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票设置类型<br />[Enum
                        values:<br />NO_INVOICE(0)<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />VAT_COMBINED(3)<br />]
                    </td>
                    <td>NO_INVOICE</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ orderCompleteDays</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>30</td>
                    <td>订单完成天数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>invoiceSettingsClientType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>所属客户端<br />[Enum values:<br />SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br />]</td>
                    <td>SHOP</td>
                <tr>
            </tbody>
        </table>
        <h4>请求示例</h4>
        <pre class="code-block">
{
    "id": 0,
    "shopId": 0,
    "invoiceSetupValue": [
        {
            "option": true,
            "invoiceType": "NO_INVOICE",
            "orderCompleteDays": 0
        }
    ],
    "invoiceSettingsClientType": "SHOP"
}
</pre>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="oNzv5LXD"><a class="link" href="#oNzv5LXD">&nbsp;获取发票设置</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: GET http://*************:9999/addon-invoice/invoice/invoiceSettings</li>
        </ul>
        <p><strong>描述：</strong>获取发票设置</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>请求参数</h4>
        <h5>Query Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>invoiceSettingsClientType</td>
                    <td>enum</td>
                    <td>是</td>
                    <td>-</td>
                    <td>SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br /></td>
                    <td>SHOP</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td></td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ id</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ createTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>创建时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ updateTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>更新时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ version</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>乐观锁版本号</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ deleted</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>逻辑删除标记</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceSettingsClientType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>所属客户端<br />[Enum values:<br />SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br />]</td>
                    <td>SHOP</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceSetupValue</td>
                    <td>array</td>
                    <td>否</td>
                    <td></td>
                    <td>发票设置json</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ option</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>是否选中</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票设置类型<br />[Enum
                        values:<br />NO_INVOICE(0)<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />VAT_COMBINED(3)<br />]
                    </td>
                    <td>NO_INVOICE</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ orderCompleteDays</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>30</td>
                    <td>订单完成天数</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {
        "id": 0,
        "createTime": "yyyy-MM-dd HH:mm:ss",
        "updateTime": "yyyy-MM-dd HH:mm:ss",
        "version": 0,
        "deleted": true,
        "shopId": 0,
        "invoiceSettingsClientType": "SHOP",
        "invoiceSetupValue": [
            {
                "option": true,
                "invoiceType": "NO_INVOICE",
                "orderCompleteDays": 0
            }
        ]
    }
}
</pre>
        <h4>错误码</h4>无
    </div>
    <h2>发票申请表 前端控制器</h2>
    <div class="doc-item">
        <h3 id="Rd8xxy8g"><a class="link" href="#Rd8xxy8g">&nbsp;申请开票-前置校验</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: GET http://*************:9999/addon-invoice/invoice/invoiceRequest/pre-request</li>
        </ul>
        <p><strong>描述：</strong>申请开票-前置校验</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>请求参数</h4>
        <h5>Query Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>invoiceOwnerType</td>
                    <td>enum</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票所属类型<br />[Enum values:<br />USER(1,
                        InvoiceConstant.USER_INVOICE_HANDLER)<br />SHOP(2,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />SUPPLIER(3,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />]
                    </td>
                    <td>USER</td>
                <tr>
                <tr>
                    <td>orderNos</td>
                    <td>array</td>
                    <td>是</td>
                    <td>-</td>
                    <td>订单号集合</td>
                    <td>,</td>
                <tr>
            </tbody>
        </table>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td></td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceStatus</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票状态<br />[Enum
                        values:<br />REQUEST_HAS_EXPIRED(1)<br />REQUEST_IN_PROCESS(2)<br />SERVER_NOT_SUPPORTED(3)<br />FAILED_INVOICE_REQUEST(4)<br />SUCCESSFULLY_INVOICED(5)<br />ALLOWED_INVOICING(6)<br />CLIENT_CANCEL_REQUEST(7)<br />NO_INVOICE_ORDER(8)<br />]
                    </td>
                    <td>REQUEST_HAS_EXPIRED</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ billMoney</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>总开票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceTypes</td>
                    <td>array</td>
                    <td>否</td>
                    <td>-</td>
                    <td>可选择的开票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>[VAT_GENERAL]</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ orderNos</td>
                    <td>array</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票订单号</td>
                    <td>,</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceInfo</td>
                    <td>array</td>
                    <td>否</td>
                    <td></td>
                    <td>开票详情信息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ orderNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>订单号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ name</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票方名称</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票方店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceTypeList</td>
                    <td>array</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>[VAT_GENERAL]</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceToType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票角色<br />[Enum values:<br />SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br />]</td>
                    <td>SHOP</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ billMoney</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>每件商品票据金额</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {
        "invoiceStatus": "REQUEST_HAS_EXPIRED",
        "billMoney": 0,
        "invoiceTypes": [
            "VAT_GENERAL"
        ],
        "orderNos": [
            0,
            0
        ],
        "invoiceInfo": [
            {
                "orderNo": "string",
                "name": "string",
                "shopId": 0,
                "invoiceTypeList": [
                    "VAT_GENERAL"
                ],
                "invoiceToType": "SHOP",
                "billMoney": 0
            }
        ]
    }
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="yq2RGN8g"><a class="link" href="#yq2RGN8g">&nbsp;申请开票</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST http://*************:9999/addon-invoice/invoice/invoiceRequest</li>
        </ul>
        <p><strong>描述：</strong>申请开票</p>
        <p><strong>ContentType：</strong>application/json</p>
        <h4>请求参数</h4>
        <h5>Body Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>invoiceOwnerType</td>
                    <td>enum</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票所属类型<br />[Enum values:<br />USER(1,
                        InvoiceConstant.USER_INVOICE_HANDLER)<br />SHOP(2,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />SUPPLIER(3,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />]
                    </td>
                    <td>USER</td>
                <tr>
                <tr>
                    <td>orderNos</td>
                    <td>array</td>
                    <td>是</td>
                    <td>-</td>
                    <td>订单号集合</td>
                    <td>,</td>
                <tr>
                <tr>
                    <td>invoiceType</td>
                    <td>enum</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>VAT_GENERAL</td>
                <tr>
                <tr>
                    <td>billingRemarks</td>
                    <td>string</td>
                    <td>否</td>
                    <td>500</td>
                    <td>开票备注<br>Validate[max: 500; ]</td>
                    <td></td>
                <tr>
                <tr>
                    <td>invoiceHeaderId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票抬头id</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>请求示例</h4>
        <pre class="code-block">
{
    "invoiceOwnerType": "USER",
    "orderNos": [
        0,
        0
    ],
    "invoiceType": "VAT_GENERAL",
    "billingRemarks": "string",
    "invoiceHeaderId": 0
}
</pre>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="bnzDrn8p"><a class="link" href="#bnzDrn8p">&nbsp;分页查询开票申请</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: GET http://*************:9999/addon-invoice/invoice/invoiceRequest</li>
        </ul>
        <p><strong>描述：</strong>分页查询开票申请</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>请求参数</h4>
        <h5>Query Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>pages</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前分页总页数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>records</td>
                    <td>array</td>
                    <td>否</td>
                    <td></td>
                    <td>查询数据列表</td>
                    <td></td>
                <tr>
                <tr>
                    <td>id</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>createTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>创建时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>updateTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>更新时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>version</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>乐观锁版本号</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>deleted</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>逻辑删除标记</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>applicantId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>applicantShopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>处理发票申请的店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>invoiceOwnerType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票所属类型<br />[Enum values:<br />USER(1,
                        InvoiceConstant.USER_INVOICE_HANDLER)<br />SHOP(2,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />SUPPLIER(3,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />]
                    </td>
                    <td>USER</td>
                <tr>
                <tr>
                    <td>invoiceToType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票所属类型<br />[Enum values:<br />SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br />]</td>
                    <td>SHOP</td>
                <tr>
                <tr>
                    <td>shopSupplierName</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>店铺或供应商名称</td>
                    <td></td>
                <tr>
                <tr>
                    <td>orderNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>订单号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>invoiceAmount</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>invoiceType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>VAT_GENERAL</td>
                <tr>
                <tr>
                    <td>invoiceStatus</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票状态<br />[Enum
                        values:<br />REQUEST_HAS_EXPIRED(1)<br />REQUEST_IN_PROCESS(2)<br />SERVER_NOT_SUPPORTED(3)<br />FAILED_INVOICE_REQUEST(4)<br />SUCCESSFULLY_INVOICED(5)<br />ALLOWED_INVOICING(6)<br />CLIENT_CANCEL_REQUEST(7)<br />NO_INVOICE_ORDER(8)<br />]
                    </td>
                    <td>REQUEST_HAS_EXPIRED</td>
                <tr>
                <tr>
                    <td>invoiceHeaderType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />PERSONAL(1)<br />ENTERPRISE(2)<br />]</td>
                    <td>PERSONAL</td>
                <tr>
                <tr>
                    <td>applicationTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>billingRemarks</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票备注</td>
                    <td></td>
                <tr>
                <tr>
                    <td>denialReason</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>拒绝原因</td>
                    <td></td>
                <tr>
                <tr>
                    <td>header</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头</td>
                    <td></td>
                <tr>
                <tr>
                    <td>taxIdentNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>税号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>openingBank</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开户行</td>
                    <td></td>
                <tr>
                <tr>
                    <td>bankAccountNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>银行账号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>enterprisePhone</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>企业电话</td>
                    <td></td>
                <tr>
                <tr>
                    <td>enterpriseAddress</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>企业地址</td>
                    <td></td>
                <tr>
                <tr>
                    <td>email</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>邮箱地址</td>
                    <td></td>
                <tr>
                <tr>
                    <td>extraMap</td>
                    <td>map</td>
                    <td>否</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td></td>
                <tr>
                <tr>
                    <td>total</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>总数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>size</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>每页显示条数，默认 10</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>current</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前页</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>orders</td>
                    <td>array</td>
                    <td>否</td>
                    <td></td>
                    <td>排序字段信息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>column</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>需要进行排序的字段</td>
                    <td></td>
                <tr>
                <tr>
                    <td>asc</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>是否正序排列，默认 true</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>optimizeCountSql</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>自动优化 COUNT SQL</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>searchCount</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>是否进行 count 查询</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>optimizeJoinOfCountSql</td>
                    <td>boolean</td>
                    <td>否</td>
                    <td>-</td>
                    <td>{@link #optimizeJoinOfCountSql()}</td>
                    <td>true</td>
                <tr>
                <tr>
                    <td>countId</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>countId</td>
                    <td></td>
                <tr>
                <tr>
                    <td>maxLimit</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>countId</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>id</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>applicantId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人id(用户端使用)</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>店铺ID</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>header</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头</td>
                    <td></td>
                <tr>
                <tr>
                    <td>taxIdentNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>税号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>orderNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>订单号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>invoiceType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>VAT_GENERAL</td>
                <tr>
                <tr>
                    <td>applicationStartTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请开始时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>applicationEndTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请结束时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>invoiceStatus</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票状态<br />[Enum
                        values:<br />REQUEST_HAS_EXPIRED(1)<br />REQUEST_IN_PROCESS(2)<br />SERVER_NOT_SUPPORTED(3)<br />FAILED_INVOICE_REQUEST(4)<br />SUCCESSFULLY_INVOICED(5)<br />ALLOWED_INVOICING(6)<br />CLIENT_CANCEL_REQUEST(7)<br />NO_INVOICE_ORDER(8)<br />]
                    </td>
                    <td>REQUEST_HAS_EXPIRED</td>
                <tr>
                <tr>
                    <td>invoiceHeaderType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头类型<br />[Enum values:<br />PERSONAL(1)<br />ENTERPRISE(2)<br />]</td>
                    <td>PERSONAL</td>
                <tr>
            </tbody>
        </table>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td></td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ pages</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前分页总页数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ records</td>
                    <td>array</td>
                    <td>否</td>
                    <td></td>
                    <td>分页记录列表</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ id</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ applicantId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ applicantShopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票方店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceOwnerType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票所属类型<br />[Enum values:<br />USER(1,
                        InvoiceConstant.USER_INVOICE_HANDLER)<br />SHOP(2,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />SUPPLIER(3,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />]
                    </td>
                    <td>USER</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceToType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票方所属类型<br />[Enum values:<br />SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br />]</td>
                    <td>SHOP</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ shopSupplierName</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>店铺或供应商名称</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ orderNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>订单号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceAmount</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ totalAmount</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>该订单总开票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>VAT_GENERAL</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceStatus</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票状态<br />[Enum
                        values:<br />REQUEST_HAS_EXPIRED(1)<br />REQUEST_IN_PROCESS(2)<br />SERVER_NOT_SUPPORTED(3)<br />FAILED_INVOICE_REQUEST(4)<br />SUCCESSFULLY_INVOICED(5)<br />ALLOWED_INVOICING(6)<br />CLIENT_CANCEL_REQUEST(7)<br />NO_INVOICE_ORDER(8)<br />]
                    </td>
                    <td>REQUEST_HAS_EXPIRED</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ applicationTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ billingRemarks</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票备注</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ denialReason</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>拒绝原因</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ createTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ updateTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>更新时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ attachments</td>
                    <td>array</td>
                    <td>否</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>,</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ invoiceHeaderType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头类型<br />[Enum values:<br />PERSONAL(1)<br />ENTERPRISE(2)<br />]</td>
                    <td>PERSONAL</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ header</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ taxIdentNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>税号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ openingBank</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开户行</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ bankAccountNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>银行账号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ enterprisePhone</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>企业电话</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ enterpriseAddress</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>企业地址</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ email</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>邮箱地址</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ total</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前满足条件总行数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ size</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>获取每页显示条数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ current</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前页</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {
        "pages": 0,
        "records": [
            {
                "id": 0,
                "applicantId": 0,
                "applicantShopId": 0,
                "shopId": 0,
                "invoiceOwnerType": "USER",
                "invoiceToType": "SHOP",
                "shopSupplierName": "string",
                "orderNo": "string",
                "invoiceAmount": 0,
                "totalAmount": 0,
                "invoiceType": "VAT_GENERAL",
                "invoiceStatus": "REQUEST_HAS_EXPIRED",
                "applicationTime": "yyyy-MM-dd HH:mm:ss",
                "billingRemarks": "string",
                "denialReason": "string",
                "createTime": "yyyy-MM-dd HH:mm:ss",
                "updateTime": "yyyy-MM-dd HH:mm:ss",
                "attachments": [
                    0,
                    0
                ],
                "invoiceHeaderType": "PERSONAL",
                "header": "string",
                "taxIdentNo": "string",
                "openingBank": "string",
                "bankAccountNo": "string",
                "enterprisePhone": "string",
                "enterpriseAddress": "string",
                "email": "string"
            }
        ],
        "total": 0,
        "size": 0,
        "current": 0
    }
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="NK8MBjXl"><a class="link" href="#NK8MBjXl">&nbsp;获取发票申请详情</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: GET http://*************:9999/addon-invoice/invoice/invoiceRequest/{id}</li>
        </ul>
        <p><strong>描述：</strong>获取发票申请详情</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>Path参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>必填</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>id</td>
                    <td>是</td>
                    <td>发票申请ID</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>请求参数</h4>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td></td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ id</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ applicantId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ applicantShopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请人店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票方店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceOwnerType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票所属类型<br />[Enum values:<br />USER(1,
                        InvoiceConstant.USER_INVOICE_HANDLER)<br />SHOP(2,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />SUPPLIER(3,InvoiceConstant.SHOP_INVOICE_HANDLER)<br />]
                    </td>
                    <td>USER</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceToType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票方所属类型<br />[Enum values:<br />SHOP(1)<br />SUPPLIER(2)<br />PLATFORM(3)<br />]</td>
                    <td>SHOP</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ shopSupplierName</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>店铺或供应商名称</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ orderNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>订单号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceAmount</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ totalAmount</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>该订单总开票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票类型<br />[Enum values:<br />VAT_GENERAL(1)<br />VAT_SPECIAL(2)<br />]</td>
                    <td>VAT_GENERAL</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceStatus</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票状态<br />[Enum
                        values:<br />REQUEST_HAS_EXPIRED(1)<br />REQUEST_IN_PROCESS(2)<br />SERVER_NOT_SUPPORTED(3)<br />FAILED_INVOICE_REQUEST(4)<br />SUCCESSFULLY_INVOICED(5)<br />ALLOWED_INVOICING(6)<br />CLIENT_CANCEL_REQUEST(7)<br />NO_INVOICE_ORDER(8)<br />]
                    </td>
                    <td>REQUEST_HAS_EXPIRED</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ applicationTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ billingRemarks</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票备注</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ denialReason</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>拒绝原因</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ createTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>申请时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ updateTime</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>更新时间</td>
                    <td>yyyy-MM-dd HH:mm:ss</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ attachments</td>
                    <td>array</td>
                    <td>否</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>,</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ invoiceHeaderType</td>
                    <td>enum</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头类型<br />[Enum values:<br />PERSONAL(1)<br />ENTERPRISE(2)<br />]</td>
                    <td>PERSONAL</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ header</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票抬头</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ taxIdentNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>税号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ openingBank</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开户行</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ bankAccountNo</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>银行账号</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ enterprisePhone</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>企业电话</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ enterpriseAddress</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>企业地址</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ email</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>邮箱地址</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {
        "id": 0,
        "applicantId": 0,
        "applicantShopId": 0,
        "shopId": 0,
        "invoiceOwnerType": "USER",
        "invoiceToType": "SHOP",
        "shopSupplierName": "string",
        "orderNo": "string",
        "invoiceAmount": 0,
        "totalAmount": 0,
        "invoiceType": "VAT_GENERAL",
        "invoiceStatus": "REQUEST_HAS_EXPIRED",
        "applicationTime": "yyyy-MM-dd HH:mm:ss",
        "billingRemarks": "string",
        "denialReason": "string",
        "createTime": "yyyy-MM-dd HH:mm:ss",
        "updateTime": "yyyy-MM-dd HH:mm:ss",
        "attachments": [
            0,
            0
        ],
        "invoiceHeaderType": "PERSONAL",
        "header": "string",
        "taxIdentNo": "string",
        "openingBank": "string",
        "bankAccountNo": "string",
        "enterprisePhone": "string",
        "enterpriseAddress": "string",
        "email": "string"
    }
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="eW2oPr8D"><a class="link" href="#eW2oPr8D">&nbsp;撤销开票</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: PUT http://*************:9999/addon-invoice/invoice/invoiceRequest/{id}</li>
        </ul>
        <p><strong>描述：</strong>撤销开票</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>Path参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>必填</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>id</td>
                    <td>是</td>
                    <td>发票申请ID</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>请求参数</h4>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="VK2ZYxzv"><a class="link" href="#VK2ZYxzv">&nbsp;拒绝开票申请</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST http://*************:9999/addon-invoice/invoice/invoiceRequest/refuseInvoiceRequest</li>
        </ul>
        <p><strong>描述：</strong>拒绝开票申请</p>
        <p><strong>ContentType：</strong>application/json</p>
        <h4>请求参数</h4>
        <h5>Body Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>id</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票申请ID</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>denialReason</td>
                    <td>string</td>
                    <td>是</td>
                    <td>15</td>
                    <td>拒绝原因<br>Validate[max: 15; ]</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>请求示例</h4>
        <pre class="code-block">
{
    "id": 0,
    "denialReason": "string"
}
</pre>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="N2elAeze"><a class="link" href="#N2elAeze">&nbsp;分页查看开票商品</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: GET http://*************:9999/addon-invoice/invoice/invoiceRequest/queryInvoiceGoodsInfo</li>
        </ul>
        <p><strong>描述：</strong>分页查看开票商品</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>请求参数</h4>
        <h5>Query Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>id</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票主键id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>current</td>
                    <td>int32</td>
                    <td>是</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>size</td>
                    <td>int32</td>
                    <td>是</td>
                    <td>-</td>
                    <td>No comments found.</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td></td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ pages</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前分页总页数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ records</td>
                    <td>array</td>
                    <td>否</td>
                    <td></td>
                    <td>分页记录列表</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ image</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>商品图片链接</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ productName</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>商品名称</td>
                    <td></td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└ amount</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>开票金额</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ total</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前满足条件总行数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ size</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>获取每页显示条数</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;└ current</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>当前页</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {
        "pages": 0,
        "records": [
            {
                "image": "string",
                "productName": "string",
                "amount": 0
            }
        ],
        "total": 0,
        "size": 0,
        "current": 0
    }
}
</pre>
        <h4>错误码</h4>无
    </div>
    <h2>发票附件表 前端控制器</h2>
    <div class="doc-item">
        <h3 id="9m8wW78k"><a class="link" href="#9m8wW78k">&nbsp;上传发票附件/重新上传发票附件</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST http://*************:9999/addon-invoice/invoice/invoiceAttachment/upload</li>
        </ul>
        <p><strong>描述：</strong>上传发票附件/重新上传发票附件</p>
        <p><strong>ContentType：</strong>application/json</p>
        <h4>请求参数</h4>
        <h5>Body Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>invoiceRequestId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>发票申请表id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>invoiceAttachmentUrl</td>
                    <td>array</td>
                    <td>是</td>
                    <td>5</td>
                    <td>发票附件URL地址</td>
                    <td>,</td>
                <tr>
            </tbody>
        </table>
        <h4>请求示例</h4>
        <pre class="code-block">
{
    "invoiceRequestId": 0,
    "shopId": 0,
    "invoiceAttachmentUrl": [
        0,
        0
    ]
}
</pre>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="3E8rGNX0"><a class="link" href="#3E8rGNX0">&nbsp;重新发送附件</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST http://*************:9999/addon-invoice/invoice/invoiceAttachment/re-send</li>
        </ul>
        <p><strong>描述：</strong>重新发送附件</p>
        <p><strong>ContentType：</strong>application/json</p>
        <h4>请求参数</h4>
        <h5>Body Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>invoiceRequestId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票申请表id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>店铺id</td>
                    <td>0</td>
                <tr>
            </tbody>
        </table>
        <h4>请求示例</h4>
        <pre class="code-block">
{
    "invoiceRequestId": 0,
    "shopId": 0
}
</pre>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="pqXbYv8v"><a class="link" href="#pqXbYv8v">&nbsp;下载发票附件</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST http://*************:9999/addon-invoice/invoice/invoiceAttachment/downloadInvoiceAttachment
            </li>
        </ul>
        <p><strong>描述：</strong>下载发票附件</p>
        <p><strong>ContentType：</strong>application/json</p>
        <h4>请求参数</h4>
        <h5>Body Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>invoiceRequestId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>发票申请表id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>shopId</td>
                    <td>int64</td>
                    <td>是</td>
                    <td>-</td>
                    <td>店铺id</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>invoiceAttachmentUrl</td>
                    <td>array</td>
                    <td>是</td>
                    <td>5</td>
                    <td>发票附件URL地址</td>
                    <td>,</td>
                <tr>
            </tbody>
        </table>
        <h4>请求示例</h4>
        <pre class="code-block">
{
    "invoiceRequestId": 0,
    "shopId": 0,
    "invoiceAttachmentUrl": [
        0,
        0
    ]
}
</pre>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>object</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": {}
}
</pre>
        <h4>错误码</h4>无
    </div>
    <div class="doc-item">
        <h3 id="K8MNWPXl"><a class="link" href="#K8MNWPXl">&nbsp;商家、供应商端导出发票附件</a></h3>
        <p><strong>URL</strong></p>
        <ul>
            <li>开发环境: POST
                http://*************:9999/addon-invoice/invoice/invoiceAttachment/downloadInvoiceAttachmentByShop</li>
        </ul>
        <p><strong>描述：</strong>商家、供应商端导出发票附件</p>
        <p><strong>ContentType：</strong>application/x-www-form-urlencoded;charset=UTF-8</p>
        <h4>请求参数</h4>
        <h5>Query Parameter</h5>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>orderNo</td>
                    <td>string</td>
                    <td>是</td>
                    <td>-</td>
                    <td>订单号</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应参数</h4>
        <table class="pure-table pure-table-bordered">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>最大长度</th>
                    <th>描述</th>
                    <th>示例值</th>
                <tr>
            </thead>
            <tbody>
                <tr>
                    <td>code</td>
                    <td>int32</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应状态码</td>
                    <td>0</td>
                <tr>
                <tr>
                    <td>msg</td>
                    <td>string</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应消息</td>
                    <td></td>
                <tr>
                <tr>
                    <td>data</td>
                    <td>int64</td>
                    <td>否</td>
                    <td>-</td>
                    <td>响应数据</td>
                    <td></td>
                <tr>
            </tbody>
        </table>
        <h4>响应示例</h4>
        <pre class="code-block">
{
    "code": 0,
    "msg": "string",
    "data": 0
}
</pre>
        <h4>错误码</h4>无
    </div>
</body>

</html>